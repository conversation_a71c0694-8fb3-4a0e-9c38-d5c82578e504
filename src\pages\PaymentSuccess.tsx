import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { CheckCircle, Loader2 } from "lucide-react";

const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, updateProfile } = useAuth();
  const [isProcessing, setIsProcessing] = useState(true);
  const [paymentDetails, setPaymentDetails] = useState<any>(null);

  useEffect(() => {
    const extractPaymentDetails = () => {
      const details = {
        status: searchParams.get("status"),
        paymentId:
          searchParams.get("payment_id") || searchParams.get("session_id"),
        plan: searchParams.get("plan"),
        amount: searchParams.get("amount"),
        transactionId: searchParams.get("transaction_id"),
      };

      console.log("Payment success parameters:", details);
      setPaymentDetails(details);

      if (details.status === "success" || details.paymentId) {
        updateUserSubscription(details);
      }

      setIsProcessing(false);
    };

    extractPaymentDetails();
  }, [searchParams]);

  const updateUserSubscription = async (details: any) => {
    if (!user) return;

    try {
      let subscriptionType = "free";
      let credits = 0;

      if (details.amount === "30" || details.plan === "basic") {
        subscriptionType = "basic";
        credits = 45;
      } else if (details.amount === "49" || details.plan === "pro") {
        subscriptionType = "pro";
        credits = 75;
      } else if (details.amount === "79" || details.plan === "enterprise") {
        subscriptionType = "enterprise";
        credits = 9999999;
      }

      await updateProfile({
        subscription: subscriptionType,
        credits: credits,
      });

      console.log(`User subscription updated to: ${subscriptionType}`);
    } catch (error) {
      console.error("Failed to update user subscription:", error);
    }
  };

  const handleContinue = () => {
    navigate("/dashboard");
  };

  if (isProcessing) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-white mx-auto mb-4" />
          <p className="text-white">Processing your payment...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg p-8 text-center">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-6" />

        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Payment Successful!
        </h1>

        <p className="text-gray-600 mb-6">
          Thank you for your purchase. Your subscription has been activated.
        </p>

        {paymentDetails && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
            <h3 className="font-semibold text-gray-900 mb-2">
              Payment Details:
            </h3>
            {paymentDetails.paymentId && (
              <p className="text-sm text-gray-600">
                <strong>Payment ID:</strong> {paymentDetails.paymentId}
              </p>
            )}
            {paymentDetails.amount && (
              <p className="text-sm text-gray-600">
                <strong>Amount:</strong> ${paymentDetails.amount}
              </p>
            )}
            {paymentDetails.plan && (
              <p className="text-sm text-gray-600">
                <strong>Plan:</strong> {paymentDetails.plan}
              </p>
            )}
          </div>
        )}

        <Button
          onClick={handleContinue}
          className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:opacity-90"
        >
          Continue to Dashboard
        </Button>

        <p className="text-xs text-gray-500 mt-4">
          You will receive a confirmation email shortly.
        </p>
      </div>
    </div>
  );
};

export default PaymentSuccess;
